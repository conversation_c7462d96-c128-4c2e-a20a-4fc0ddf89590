#!/bin/bash

# Create logs directory if it doesn't exist
mkdir -p /app/logs

# Set proper permissions
chmod 755 /app/logs
chmod 666 /app/logs/sbir_api.log 2>/dev/null || true

# Create log file if it doesn't exist
touch /app/logs/sbir_api.log

# Set permissions for log file
chmod 666 /app/logs/sbir_api.log

echo "Starting DesignFlow Backend..."
echo "Log directory: $(ls -la /app/logs/)"

# Start the application
exec gunicorn --bind 0.0.0.0:5000 --access-logfile - --error-logfile - --log-level info app:app
