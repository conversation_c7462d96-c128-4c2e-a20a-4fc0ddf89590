import os
import json
import time
from flask import jsonify, request
from utils.logger import logger
from config import LOG_FILE_PATH

def get_logs():
    """Get logs from the sbir_api.log file with optional filtering and pagination."""
    try:
        # Get query parameters
        lines = request.args.get('lines', 100, type=int)  # Number of lines to return
        level = request.args.get('level', None)  # Filter by log level (INFO, ERROR, etc.)
        search = request.args.get('search', None)  # Search term

        # Ensure lines is reasonable
        lines = min(lines, 1000)  # Max 1000 lines

        # Check if log file exists
        if not os.path.exists(LOG_FILE_PATH):
            return jsonify({
                "success": False,
                "message": "Log file not found",
                "logs": []
            }), 404

        # Read the log file
        with open(LOG_FILE_PATH, 'r', encoding='utf-8') as file:
            all_lines = file.readlines()

        # Get the last N lines
        recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

        # Parse and filter logs
        parsed_logs = []
        for line in recent_lines:
            line = line.strip()
            if not line:
                continue

            # Parse log line
            log_entry = parse_log_line(line)

            # Apply filters
            if level and log_entry.get('level') != level:
                continue

            if search and search.lower() not in line.lower():
                continue

            parsed_logs.append(log_entry)

        return jsonify({
            "success": True,
            "logs": parsed_logs,
            "total": len(parsed_logs),
            "file_size": os.path.getsize(LOG_FILE_PATH),
            "timestamp": time.time(),
            "filters_applied": {
                "level": level,
                "search": search,
                "lines": lines
            }
        }), 200

    except Exception as e:
        logger.error(f"Error reading logs: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error reading logs: {str(e)}",
            "logs": []
        }), 500

def get_live_logs():
    """Get the latest logs for live monitoring."""
    try:
        # Get the last 50 lines by default for live view
        lines = request.args.get('lines', 50, type=int)
        lines = min(lines, 200)  # Max 200 lines for live view

        if not os.path.exists(LOG_FILE_PATH):
            return jsonify({
                "success": False,
                "message": "Log file not found",
                "logs": []
            }), 404

        # Read the log file
        with open(LOG_FILE_PATH, 'r', encoding='utf-8') as file:
            all_lines = file.readlines()

        # Get the last N lines
        recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

        # Parse logs
        parsed_logs = []
        for line in recent_lines:
            line = line.strip()
            if line:
                parsed_logs.append(parse_log_line(line))

        return jsonify({
            "success": True,
            "logs": parsed_logs,
            "timestamp": time.time(),
            "file_modified": os.path.getmtime(LOG_FILE_PATH),
            "file_size": os.path.getsize(LOG_FILE_PATH),
            "live_mode": True
        }), 200

    except Exception as e:
        logger.error(f"Error reading live logs: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error reading live logs: {str(e)}",
            "logs": []
        }), 500

def parse_log_line(line):
    """Parse a log line into structured data."""
    try:
        # Expected format: 2025-04-13 20:49:31,521 - API - INFO - image_model.py:15 - Loading model from static/model.keras
        parts = line.split(' - ', 4)

        if len(parts) >= 4:
            timestamp = parts[0]
            logger_name = parts[1]
            level = parts[2]
            location = parts[3]
            message = parts[4] if len(parts) > 4 else ""

            # Extract file and line number from location
            file_info = location.split(':')
            filename = file_info[0] if file_info else ""
            line_number = file_info[1] if len(file_info) > 1 else ""

            return {
                "timestamp": timestamp,
                "logger": logger_name,
                "level": level,
                "filename": filename,
                "line_number": line_number,
                "message": message,
                "raw": line
            }
        else:
            # If parsing fails, return raw line
            return {
                "timestamp": "",
                "logger": "",
                "level": "UNKNOWN",
                "filename": "",
                "line_number": "",
                "message": line,
                "raw": line
            }

    except Exception as e:
        # If parsing fails, return raw line
        return {
            "timestamp": "",
            "logger": "",
            "level": "ERROR",
            "filename": "",
            "line_number": "",
            "message": f"Parse error: {line}",
            "raw": line
        }

def clear_logs():
    """Clear the log file (admin function)."""
    try:
        if os.path.exists(LOG_FILE_PATH):
            # Backup current logs before clearing
            backup_path = LOG_FILE_PATH + '.backup'
            with open(LOG_FILE_PATH, 'r') as src, open(backup_path, 'w') as dst:
                dst.write(src.read())

            # Clear the log file
            with open(LOG_FILE_PATH, 'w') as file:
                file.write("")

            logger.info("Log file cleared by user")
            return jsonify({
                "success": True,
                "message": "Logs cleared successfully. Backup created."
            }), 200
        else:
            return jsonify({
                "success": False,
                "message": "Log file not found"
            }), 404

    except Exception as e:
        logger.error(f"Error clearing logs: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error clearing logs: {str(e)}"
        }), 500

def get_log_stats():
    """Get log file statistics."""
    try:
        if not os.path.exists(LOG_FILE_PATH):
            return jsonify({
                "success": False,
                "message": "Log file not found",
                "stats": {}
            }), 404

        # Read the log file
        with open(LOG_FILE_PATH, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # Count log levels
        level_counts = {
            'INFO': 0,
            'WARNING': 0,
            'ERROR': 0,
            'DEBUG': 0,
            'CRITICAL': 0,
            'UNKNOWN': 0
        }

        for line in lines:
            line = line.strip()
            if line:
                log_entry = parse_log_line(line)
                level = log_entry.get('level', 'UNKNOWN').upper()
                if level in level_counts:
                    level_counts[level] += 1
                else:
                    level_counts['UNKNOWN'] += 1

        file_stats = os.stat(LOG_FILE_PATH)

        return jsonify({
            "success": True,
            "stats": {
                "total_lines": len(lines),
                "level_counts": level_counts,
                "file_size": file_stats.st_size,
                "file_size_mb": round(file_stats.st_size / (1024 * 1024), 2),
                "created": file_stats.st_ctime,
                "modified": file_stats.st_mtime,
                "accessed": file_stats.st_atime
            }
        }), 200

    except Exception as e:
        logger.error(f"Error getting log stats: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error getting log stats: {str(e)}",
            "stats": {}
        }), 500
