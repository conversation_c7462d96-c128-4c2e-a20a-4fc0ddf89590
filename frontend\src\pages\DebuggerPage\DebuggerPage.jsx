import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './DebuggerPage.css';

const DebuggerPage = () => {
  const [logs, setLogs] = useState([]);
  const [isLive, setIsLive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [filters, setFilters] = useState({
    level: '',
    search: '',
    lines: 100
  });
  const [autoScroll, setAutoScroll] = useState(true);
  const [lastTimestamp, setLastTimestamp] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  const logsContainerRef = useRef(null);
  const intervalRef = useRef(null);
  const retryTimeoutRef = useRef(null);
  const abortControllerRef = useRef(null);

  // API base URL
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

  // Constants
  const MAX_RETRY_ATTEMPTS = 3;
  const RETRY_DELAY = 2000;
  const LIVE_UPDATE_INTERVAL = 2000;

  // Cleanup function
  const cleanup = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // Enhanced fetch logs with retry logic and abort controller
  const fetchLogs = useCallback(async (isLiveUpdate = false) => {
    try {
      if (!isLiveUpdate) {
        setLoading(true);
        setConnectionStatus('connecting');
      }
      setError(null);

      // Create abort controller for this request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      const params = new URLSearchParams();
      if (filters.lines) params.append('lines', filters.lines);
      if (filters.level) params.append('level', filters.level);
      if (filters.search) params.append('search', filters.search);

      const endpoint = isLive ? '/logs/live' : '/logs';
      const response = await fetch(`${API_BASE_URL}${endpoint}?${params}`, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setLogs(data.logs);
        if (data.timestamp) {
          setLastTimestamp(data.timestamp);
        }
        setConnectionStatus('connected');
        setRetryCount(0);
        setIsRetrying(false);

        // Auto scroll to bottom if enabled
        if (autoScroll && logsContainerRef.current) {
          setTimeout(() => {
            logsContainerRef.current.scrollTop = logsContainerRef.current.scrollHeight;
          }, 100);
        }
      } else {
        throw new Error(data.message || 'Failed to fetch logs');
      }
    } catch (err) {
      if (err.name === 'AbortError') {
        return; // Request was aborted, don't show error
      }

      const errorMessage = `Error fetching logs: ${err.message}`;
      setError(errorMessage);
      setConnectionStatus('error');
      console.error('Error fetching logs:', err);

      // Implement retry logic for live updates
      if (isLive && retryCount < MAX_RETRY_ATTEMPTS) {
        setIsRetrying(true);
        setRetryCount(prev => prev + 1);
        retryTimeoutRef.current = setTimeout(() => {
          fetchLogs(true);
        }, RETRY_DELAY);
      }
    } finally {
      if (!isLiveUpdate) {
        setLoading(false);
      }
    }
  }, [filters, isLive, autoScroll, retryCount]);

  // Enhanced clear logs with confirmation
  const clearLogs = useCallback(async () => {
    if (!window.confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/logs/clear`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setLogs([]);
        setLastTimestamp(null);
        // Show success message briefly
        setError(null);
      } else {
        throw new Error(data.message || 'Failed to clear logs');
      }
    } catch (err) {
      setError(`Error clearing logs: ${err.message}`);
      console.error('Error clearing logs:', err);
    } finally {
      setLoading(false);
    }
  }, [API_BASE_URL]);

  // Enhanced live monitoring with better state management
  const toggleLiveMode = useCallback(() => {
    if (isLive) {
      // Stop live mode
      cleanup();
      setIsLive(false);
      setConnectionStatus('disconnected');
      setRetryCount(0);
      setIsRetrying(false);
    } else {
      // Start live mode
      setIsLive(true);
      setConnectionStatus('connecting');
      fetchLogs(true); // Initial fetch
      intervalRef.current = setInterval(() => {
        fetchLogs(true);
      }, LIVE_UPDATE_INTERVAL);
    }
  }, [isLive, cleanup, fetchLogs]);

  // Enhanced filter handling with debouncing
  const handleFilterChange = useCallback((key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  // Apply filters with loading state
  const applyFilters = useCallback(() => {
    fetchLogs();
  }, [fetchLogs]);

  // Debounced search to avoid too many API calls
  const debouncedSearch = useMemo(() => {
    const timeoutRef = { current: null };
    return (searchTerm) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        handleFilterChange('search', searchTerm);
        if (searchTerm !== filters.search) {
          fetchLogs();
        }
      }, 500);
    };
  }, [handleFilterChange, fetchLogs, filters.search]);

  // Enhanced log level color with more levels
  const getLogLevelColor = useCallback((level) => {
    switch (level?.toUpperCase()) {
      case 'ERROR':
      case 'CRITICAL':
        return '#e74c3c';
      case 'WARNING':
      case 'WARN':
        return '#f39c12';
      case 'INFO':
        return '#3498db';
      case 'DEBUG':
        return '#9b59b6';
      case 'SUCCESS':
        return '#2ecc71';
      default:
        return '#95a5a6';
    }
  }, []);

  // Enhanced timestamp formatting
  const formatTimestamp = useCallback((timestamp) => {
    if (!timestamp) return '';

    // If timestamp is already a formatted string (like "2025-05-28 21:35:52,305"), return as is
    if (typeof timestamp === 'string' && timestamp.includes('-') && timestamp.includes(':')) {
      return timestamp;
    }

    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return timestamp; // Return original if invalid date
      }
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    } catch (e) {
      return timestamp;
    }
  }, []);

  // Get connection status indicator
  const getConnectionStatusIndicator = useCallback(() => {
    switch (connectionStatus) {
      case 'connected':
        return { icon: '🟢', text: 'Connected', color: '#2ecc71' };
      case 'connecting':
        return { icon: '🟡', text: 'Connecting...', color: '#f39c12' };
      case 'error':
        return { icon: '🔴', text: 'Connection Error', color: '#e74c3c' };
      default:
        return { icon: '⚫', text: 'Disconnected', color: '#95a5a6' };
    }
  }, [connectionStatus]);

  // Initial load
  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  // Cleanup on unmount and when dependencies change
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  // Auto-retry logic when connection fails
  useEffect(() => {
    if (connectionStatus === 'error' && isLive && retryCount < MAX_RETRY_ATTEMPTS) {
      const retryTimeout = setTimeout(() => {
        setIsRetrying(true);
        fetchLogs(true);
      }, RETRY_DELAY * Math.pow(2, retryCount)); // Exponential backoff

      return () => clearTimeout(retryTimeout);
    }
  }, [connectionStatus, isLive, retryCount, fetchLogs]);

  return (
    <motion.div
      className="debugger-page"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="debugger-header">
        <h1>Live Log Debugger</h1>
        <p>Monitor real-time logs from the SBIR API</p>
      </div>

      {/* Controls */}
      <div className="debugger-controls">
        <div className="control-group">
          <label>Lines:</label>
          <select
            value={filters.lines}
            onChange={(e) => handleFilterChange('lines', e.target.value)}
          >
            <option value={50}>50</option>
            <option value={100}>100</option>
            <option value={200}>200</option>
            <option value={500}>500</option>
          </select>
        </div>

        <div className="control-group">
          <label>Level:</label>
          <select
            value={filters.level}
            onChange={(e) => handleFilterChange('level', e.target.value)}
          >
            <option value="">All</option>
            <option value="INFO">INFO</option>
            <option value="ERROR">ERROR</option>
            <option value="WARNING">WARNING</option>
            <option value="DEBUG">DEBUG</option>
          </select>
        </div>

        <div className="control-group">
          <label>Search:</label>
          <input
            type="text"
            value={filters.search}
            onChange={(e) => debouncedSearch(e.target.value)}
            placeholder="Search logs..."
            disabled={loading}
          />
        </div>

        <div className="control-buttons">
          <button onClick={applyFilters} disabled={loading}>
            {loading ? 'Loading...' : 'Apply Filters'}
          </button>

          <button
            onClick={toggleLiveMode}
            className={isLive ? 'live-active' : ''}
          >
            {isLive ? '⏸️ Stop Live' : '▶️ Start Live'}
          </button>

          <button onClick={clearLogs} disabled={loading} className="clear-btn">
            🗑️ Clear Logs
          </button>
        </div>

        <div className="control-group">
          <label>
            <input
              type="checkbox"
              checked={autoScroll}
              onChange={(e) => setAutoScroll(e.target.checked)}
            />
            Auto Scroll
          </label>
        </div>
      </div>

      {/* Enhanced Status */}
      <div className="debugger-status">
        <span
          className={`status-indicator ${connectionStatus}`}
          style={{ color: getConnectionStatusIndicator().color }}
        >
          {getConnectionStatusIndicator().icon} {getConnectionStatusIndicator().text}
        </span>
        <span>Total Logs: {logs.length}</span>
        {lastTimestamp && (
          <span>Last Update: {new Date(lastTimestamp * 1000).toLocaleTimeString()}</span>
        )}
        {isRetrying && (
          <span className="retry-indicator">
            🔄 Retrying... (Attempt {retryCount}/{MAX_RETRY_ATTEMPTS})
          </span>
        )}
      </div>

      {/* Enhanced Error Display */}
      <AnimatePresence>
        {error && (
          <motion.div
            className="error-message"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="error-content">
              <span className="error-icon">⚠️</span>
              <div className="error-text">
                <strong>Error:</strong> {error}
              </div>
              <button
                className="error-dismiss"
                onClick={() => setError(null)}
                title="Dismiss error"
              >
                ✕
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Logs Container */}
      <div className="logs-container" ref={logsContainerRef}>
        {logs.length === 0 ? (
          <div className="no-logs">
            {loading ? 'Loading logs...' : 'No logs found'}
          </div>
        ) : (
          logs.map((log, index) => (
            <motion.div
              key={index}
              className="log-entry"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.01 }}
            >
              <div className="log-header">
                <span className="log-timestamp">
                  {formatTimestamp(log.timestamp)}
                </span>
                <span
                  className="log-level"
                  style={{ color: getLogLevelColor(log.level) }}
                >
                  {log.level}
                </span>
                <span className="log-location">
                  {log.filename}:{log.line_number}
                </span>
              </div>
              <div className="log-message">
                {log.message}
              </div>
            </motion.div>
          ))
        )}
      </div>
    </motion.div>
  );
};

export default DebuggerPage;
