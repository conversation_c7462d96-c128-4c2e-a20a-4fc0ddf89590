# AWS Deployment Guide with Logs Fix

## 🚨 **Issue Fixed**: Logs not appearing in AWS deployment

### **Root Cause**
The logs were not persisting in AWS because:
1. Log files were created inside Docker containers without volume mounting
2. Container restarts would lose all log data
3. No proper permissions for log file creation in production

### **Solution Applied**
1. **Volume Mounting**: Added logs volume to docker-compose.deploy.yml
2. **Dockerfile Updates**: Enhanced with proper log directory creation and permissions
3. **Startup Script**: Added initialization script for robust log setup

## 📋 **Updated Deployment Steps**

### **Step 1: Build and Push Updated Images**

```bash
# Build backend image
cd backend
docker build -t daniyal003/designflow-backend:v1.0.3 .
docker push daniyal003/designflow-backend:v1.0.3

# Build frontend image  
cd ../frontend
docker build -t daniyal003/designflow-frontend:v1.0.3 .
docker push daniyal003/designflow-frontend:v1.0.3
```

### **Step 2: Update docker-compose.deploy.yml**

Update the image versions in your docker-compose.deploy.yml:

```yaml
version: '3'

services:
  backend:
    image: daniyal003/designflow-backend:v1.0.3  # Updated version
    container_name: designflow-backend
    restart: always
    environment:
      - FLASK_ENV=production
    ports:
      - "5000:5000"
    volumes:
      - ./logs:/app/logs  # This mounts logs to host
    networks:
      - app-network

  frontend:
    image: daniyal003/designflow-frontend:v1.0.3  # Updated version
    container_name: designflow-frontend
    restart: always
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
```

### **Step 3: Deploy to AWS**

```bash
# On your AWS EC2 instance:

# 1. Create logs directory on host
mkdir -p logs
chmod 755 logs

# 2. Pull updated images
docker pull daniyal003/designflow-backend:v1.0.3
docker pull daniyal003/designflow-frontend:v1.0.3

# 3. Stop existing containers
docker-compose -f docker-compose.deploy.yml down

# 4. Start with new images
docker-compose -f docker-compose.deploy.yml up -d

# 5. Check logs are working
docker logs designflow-backend
ls -la logs/
```

### **Step 4: Verify Logs are Working**

```bash
# Check if log file is created
ls -la logs/sbir_api.log

# Check log content
tail -f logs/sbir_api.log

# Test API to generate logs
curl http://your-aws-ip:5000/

# Check debugger interface
# Visit: http://your-aws-ip:3000/debugger
```

## 🔧 **What Was Fixed**

### **1. Docker Compose Changes**
- Added volume mounting: `./logs:/app/logs`
- This ensures logs persist on the host machine

### **2. Dockerfile Enhancements**
- Added log directory creation with proper permissions
- Added startup script for robust initialization
- Enhanced Gunicorn configuration

### **3. Startup Script (start.sh)**
- Creates log directory if missing
- Sets proper permissions
- Creates log file with correct permissions
- Provides debugging output

### **4. Production Logging**
- Logs now persist across container restarts
- Proper file permissions in production environment
- Both console and file logging enabled

## 🚀 **Expected Results**

After deployment, you should see:

1. **Log file created**: `logs/sbir_api.log` on your AWS host
2. **Debugger working**: Visit `/debugger` page and see real-time logs
3. **Persistent logs**: Logs survive container restarts
4. **IP addresses**: All requests show origin IP addresses

## 🔍 **Troubleshooting**

If logs still don't appear:

```bash
# Check container logs
docker logs designflow-backend

# Check file permissions
ls -la logs/

# Check if log file exists
cat logs/sbir_api.log

# Test log endpoint directly
curl http://localhost:5000/logs

# Check container file system
docker exec -it designflow-backend ls -la /app/logs/
```

## 📝 **Important Notes**

1. **Version Update**: Make sure to use v1.0.3 or later
2. **Host Directory**: The `./logs` directory will be created on your AWS host
3. **Permissions**: The startup script handles all permission issues
4. **Persistence**: Logs now survive container restarts and updates

This fix ensures your debugger interface will work perfectly in AWS deployment! 🎉
