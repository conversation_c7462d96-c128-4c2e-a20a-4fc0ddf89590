.debugger-page {
  padding: var(--spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
  font-family: var(--font-family);
  background-color: var(--surface-color);
  min-height: calc(100vh - var(--header-height));
  color: var(--text-primary);
}

.debugger-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.debugger-header h1 {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

.debugger-header p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin: 0;
}

.debugger-controls {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  align-items: center;
  background-color: var(--surface-light);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.control-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.control-group label {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  white-space: nowrap;
}

.control-group select,
.control-group input[type="text"] {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--surface-lighter);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  min-width: 120px;
  transition: var(--transition-fast);
}

.control-group select:disabled,
.control-group input[type="text"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--surface-color);
}

.control-group select:focus,
.control-group input[type="text"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(39, 88, 107, 0.2);
}

.control-group input[type="text"]::placeholder {
  color: var(--text-tertiary);
}

.control-buttons {
  display: flex;
  gap: var(--spacing-sm);
  margin-left: auto;
}

.control-buttons button {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: var(--font-size-sm);
  min-height: 40px;
}

.control-buttons button:first-child {
  background-color: var(--primary-color);
  color: var(--text-primary);
}

.control-buttons button:first-child:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.control-buttons button:first-child:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  background-color: var(--surface-lighter);
}

.control-buttons .live-active {
  background-color: var(--error-color);
  color: var(--text-primary);
}

.control-buttons button:not(.live-active):nth-child(2) {
  background-color: var(--success-color);
  color: var(--text-primary);
}

.control-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.control-buttons .clear-btn {
  background-color: var(--error-color);
  color: var(--text-primary);
}

.debugger-status {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
  background-color: var(--surface-light);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-sm);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.status-indicator {
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  background-color: var(--surface-lighter);
}

.status-indicator.connected {
  background-color: rgba(76, 175, 80, 0.2);
  color: var(--success-color);
}

.status-indicator.error {
  background-color: rgba(244, 67, 54, 0.2);
  color: var(--error-color);
}

.status-indicator.connecting {
  background-color: rgba(255, 152, 0, 0.2);
  color: var(--warning-color);
}

.status-indicator.disconnected {
  background-color: var(--surface-lighter);
  color: var(--text-tertiary);
}

.error-message {
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid var(--error-color);
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-sm);
  box-shadow: var(--shadow-sm);
}

.error-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.error-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
  color: var(--error-color);
}

.error-text {
  flex: 1;
  color: var(--text-primary);
}

.error-dismiss {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: var(--font-size-lg);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
  flex-shrink: 0;
}

.error-dismiss:hover {
  background-color: var(--hover-color);
  color: var(--text-primary);
}

.retry-indicator {
  color: var(--warning-color);
  font-weight: var(--font-weight-semibold);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.logs-container {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  height: 600px;
  overflow-y: auto;
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  font-family: 'Courier New', monospace;
}

.logs-container::-webkit-scrollbar {
  width: 8px;
}

.logs-container::-webkit-scrollbar-track {
  background-color: var(--surface-light);
  border-radius: var(--border-radius-sm);
}

.logs-container::-webkit-scrollbar-thumb {
  background-color: var(--surface-lighter);
  border-radius: var(--border-radius-sm);
}

.logs-container::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary-color);
}

.no-logs {
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  padding: var(--spacing-xl);
}

.log-entry {
  background-color: var(--surface-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  transition: var(--transition-normal);
}

.log-entry:hover {
  background-color: var(--surface-lighter);
  border-color: var(--primary-color);
  transform: translateX(4px);
  box-shadow: var(--shadow-sm);
}

.log-header {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.log-timestamp {
  color: var(--accent-color);
  font-weight: var(--font-weight-semibold);
  min-width: 180px;
  font-family: 'Courier New', monospace;
}

.log-level {
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  background-color: var(--surface-lighter);
  min-width: 60px;
  text-align: center;
  font-size: var(--font-size-xs);
}

.log-location {
  color: var(--warning-color);
  font-weight: var(--font-weight-medium);
  font-family: 'Courier New', monospace;
}

.log-message {
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  word-break: break-word;
  padding-left: var(--spacing-sm);
  border-left: 3px solid var(--primary-color);
  font-family: 'Courier New', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .debugger-page {
    padding: var(--spacing-md);
  }

  .debugger-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .control-buttons {
    margin-left: 0;
    justify-content: center;
  }

  .debugger-status {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .log-timestamp {
    min-width: auto;
  }

  .logs-container {
    height: 400px;
  }
}

/* Animation for new log entries */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.log-entry {
  animation: slideIn 0.3s ease-out;
}
